/**
 * Chunk Loading Error Handler
 *
 * Detects when dynamic imports fail due to missing chunks (usually after deployments)
 * and automatically reloads the page to get fresh assets.
 */

let isReloading = false;

/**
 * Checks if an error is a chunk loading failure
 */
function isChunkLoadError(error) {
  const message = error?.message || '';

  // Common patterns for chunk loading errors
  const chunkLoadPatterns = [
    /Loading chunk \d+ failed/,
    /Failed to fetch dynamically imported module/,
    /Loading CSS chunk \d+ failed/,
    /ChunkLoadError/,
    /Loading chunk .+ failed/,
    /Failed to import/,
    /NetworkError.*chunk/i,
    /TypeError.*dynamically imported module/i
  ];

  return chunkLoadPatterns.some(pattern => pattern.test(message));
}

/**
 * Handles chunk loading errors by reloading the page
 */
function handleChunkLoadError(error) {
  if (isReloading) {
    return; // Prevent multiple reloads
  }

  if (isChunkLoadError(error)) {
    console.warn('Chunk loading failed, reloading page to get fresh assets:', error.message);
    isReloading = true;

    // Very short delay to show loading state before reloading
    setTimeout(() => {
      window.location.reload();
    }, 800); // 0.8 seconds - fast and seamless

    return true; // Indicates error was handled
  }

  return false; // Error was not a chunk loading error
}

/**
 * Sets up global error handlers for chunk loading failures
 */
export function setupChunkLoadingErrorHandler() {
  // Handle unhandled promise rejections (common for dynamic imports)
  window.addEventListener('unhandledrejection', (event) => {
    if (handleChunkLoadError(event.reason)) {
      event.preventDefault(); // Prevent the error from being logged
    }
  });

  // Handle general JavaScript errors
  window.addEventListener('error', (event) => {
    if (handleChunkLoadError(event.error)) {
      event.preventDefault(); // Prevent the error from being logged
    }
  });

  // Handle React Router errors specifically
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const message = args.join(' ');
    if (message.includes('Failed to fetch dynamically imported module') ||
        message.includes('ChunkLoadError') ||
        message.includes('Loading chunk') && message.includes('failed') ||
        message.includes('React Router caught the following error during render')) {

      const error = new Error(message);
      if (isChunkLoadError(error)) {
        // Immediately show update screen and reload
        showUpdateScreenAndReload();
        return; // Don't log the error if we're handling it
      }
    }

    // Call original console.error for other errors
    originalConsoleError.apply(console, args);
  };
}

/**
 * React error boundary helper for chunk loading errors
 */
export function isChunkLoadingError(error) {
  return isChunkLoadError(error);
}

/**
 * Reload page if chunk loading error is detected
 */
export function reloadOnChunkError(error) {
  return handleChunkLoadError(error);
}

/**
 * Shows a simple loading screen and reloads the page
 */
export function showUpdateScreenAndReload() {
  if (isReloading) {
    return; // Already handling
  }

  isReloading = true;

  // Create a simple full-screen loading overlay
  const loadingScreen = document.createElement('div');
  loadingScreen.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999999;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  // Add a simple spinner (matching your app's style)
  loadingScreen.innerHTML = `
    <div style="
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #520bf0;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    "></div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  `;

  document.body.appendChild(loadingScreen);

  // Reload after a short delay - fast enough to be seamless
  setTimeout(() => {
    window.location.reload();
  }, 800);
}
